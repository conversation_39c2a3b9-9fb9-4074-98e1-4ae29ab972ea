// React Native 0.74.4 için Gradle plugin - EN BAŞTA OLMALI
pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

rootProject.name = 'Fishivo'

// Monorepo için node_modules yo<PERSON><PERSON> a<PERSON>
def nodeModulesPath = new File(settingsDir, "../../../node_modules")

// Native modules autolinking için
apply from: new File(nodeModulesPath, "@react-native-community/cli-platform-android/native_modules.gradle")
applyNativeModulesSettingsGradle(settings)

include ':app'