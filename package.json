{"name": "fishivo-monorepo", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "dev:mobile": "turbo run dev --filter=@fishivo/mobile", "dev:backend": "turbo run dev --filter=@fishivo/backend", "dev:web": "turbo run dev --filter=@fishivo/web", "build:mobile": "turbo run build --filter=@fishivo/mobile", "build:backend": "turbo run build --filter=@fishivo/backend", "build:web": "turbo run build --filter=@fishivo/web", "start": "turbo run start --filter=@fishivo/backend", "start:backend": "turbo run start --filter=@fishivo/backend", "start:web": "turbo run start --filter=@fishivo/web", "android": "turbo run android --filter=@fishivo/mobile", "ios": "turbo run ios --filter=@fishivo/mobile", "pods": "turbo run pods --filter=@fishivo/mobile"}, "devDependencies": {"concurrently": "8.2.1", "eslint": "8.49.0", "prettier": "3.0.3", "turbo": "^2.5.4", "typescript": "5.2.2"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "typescript": "5.2.2", "react-native": "0.77.2", "@babel/core": "7.26.0", "@babel/runtime": "7.26.0", "@supabase/supabase-js": "2.50.2", "axios": "1.6.2", "eslint": "8.49.0"}, "keywords": ["fishivo", "fishing", "social", "platform", "monorepo", "react-native", "nextjs", "node", "mobile", "web", "backend"], "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0"}, "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c"}